defmodule Drops.SQL.Compilers.SqliteTest do
  use ExUnit.Case, async: true

  alias Drops.SQL.Compilers.Sqlite

  describe "visit/2 for types" do
    test "normalizes SQLite types" do
      assert Sqlite.visit({:type, "INTEGER"}, []) == "INTEGER"
      assert Sqlite.visit({:type, "TEXT"}, []) == "TEXT"
      assert Sqlite.visit({:type, "REAL"}, []) == "REAL"
      assert Sqlite.visit({:type, "BLOB"}, []) == "BLOB"
      assert Sqlite.visit({:type, "BOOLEAN"}, []) == "BOOLEAN"
    end

    test "extracts base types from parameterized types" do
      assert Sqlite.visit({:type, "VARCHAR(255)"}, []) == "VARCHAR"
      assert Sqlite.visit({:type, "CHAR(10)"}, []) == "CHAR"
      assert Sqlite.visit({:type, "DECIMAL(10,2)"}, []) == "DECIMAL"
      assert Sqlite.visit({:type, "NUMERIC(8,3)"}, []) == "NUMERIC"
    end

    test "handles case normalization" do
      assert Sqlite.visit({:type, "integer"}, []) == "INTEGER"
      assert Sqlite.visit({:type, "varchar(255)"}, []) == "VARCHAR"
      assert Sqlite.visit({:type, "Text"}, []) == "TEXT"
    end
  end

  describe "extract_type_parameters/1" do
    test "extracts precision and scale from DECIMAL types" do
      assert Sqlite.extract_type_parameters("DECIMAL(10,2)") == {"DECIMAL", 10, 2}
      assert Sqlite.extract_type_parameters("NUMERIC(8,3)") == {"NUMERIC", 8, 3}
    end

    test "extracts precision from VARCHAR types" do
      assert Sqlite.extract_type_parameters("VARCHAR(255)") == {"VARCHAR", 255, nil}
      assert Sqlite.extract_type_parameters("CHAR(10)") == {"CHAR", 10, nil}
    end

    test "handles types without parameters" do
      assert Sqlite.extract_type_parameters("INTEGER") == {"INTEGER", nil, nil}
      assert Sqlite.extract_type_parameters("TEXT") == {"TEXT", nil, nil}
    end
  end

  describe "visit/2 for defaults" do
    test "handles NULL values" do
      assert Sqlite.visit({:default, nil}, []) == nil
      assert Sqlite.visit({:default, ""}, []) == nil
      assert Sqlite.visit({:default, "NULL"}, []) == nil
      assert Sqlite.visit({:default, "null"}, []) == nil
    end

    test "handles quoted strings" do
      assert Sqlite.visit({:default, "'hello'"}, []) == "hello"
      assert Sqlite.visit({:default, "\"world\""}, []) == "world"
      assert Sqlite.visit({:default, "'don''t'"}, []) == "don't"
    end

    test "handles numeric values" do
      assert Sqlite.visit({:default, "123"}, []) == 123
      assert Sqlite.visit({:default, "45.67"}, []) == 45.67
      assert Sqlite.visit({:default, "-89"}, []) == -89
    end

    test "handles boolean values" do
      assert Sqlite.visit({:default, "true"}, []) == true
      assert Sqlite.visit({:default, "false"}, []) == false
      assert Sqlite.visit({:default, "TRUE"}, []) == true
      assert Sqlite.visit({:default, "FALSE"}, []) == false
    end

    test "handles SQLite functions" do
      assert Sqlite.visit({:default, "CURRENT_TIMESTAMP"}, []) == :current_timestamp
      assert Sqlite.visit({:default, "CURRENT_DATE"}, []) == :current_date
      assert Sqlite.visit({:default, "CURRENT_TIME"}, []) == :current_time
    end

    test "handles hex binary values" do
      result = Sqlite.visit({:default, "x'48656C6C6F'"}, [])
      assert result == "Hello"
    end
  end

  describe "visit/2 for meta" do
    test "adds precision and scale from opts" do
      meta = %{nullable: true, default: nil}
      opts = [precision: 10, scale: 2]

      result = Sqlite.visit({:meta, meta}, opts)

      assert result.precision == 10
      assert result.scale == 2
      assert result.nullable == true
      assert result.default == nil
    end

    test "adds only precision when scale is nil" do
      meta = %{nullable: false}
      opts = [precision: 255]

      result = Sqlite.visit({:meta, meta}, opts)

      assert result.precision == 255
      assert Map.has_key?(result, :scale) == false
    end

    test "preserves original meta when no precision/scale" do
      meta = %{nullable: true, default: "test"}
      opts = []

      result = Sqlite.visit({:meta, meta}, opts)

      assert result == meta
    end
  end

  describe "type normalization edge cases" do
    test "handles case-insensitive type normalization" do
      assert Sqlite.visit({:type, "integer"}, []) == "INTEGER"
      assert Sqlite.visit({:type, "INTEGER"}, []) == "INTEGER"
      assert Sqlite.visit({:type, "Integer"}, []) == "INTEGER"
    end

    test "handles types with extra whitespace" do
      assert Sqlite.visit({:type, " INTEGER "}, []) == "INTEGER"
      assert Sqlite.visit({:type, " VARCHAR(255) "}, []) == "VARCHAR"
    end

    test "handles unknown types" do
      assert Sqlite.visit({:type, "UNKNOWN_TYPE"}, []) == "UNKNOWN_TYPE"
      assert Sqlite.visit({:type, "CUSTOM"}, []) == "CUSTOM"
    end

    test "handles complex parameterized types" do
      assert Sqlite.visit({:type, "DECIMAL(10,2)"}, []) == "DECIMAL"
      assert Sqlite.visit({:type, "NUMERIC(8,3)"}, []) == "NUMERIC"
      assert Sqlite.visit({:type, "VARCHAR(255)"}, []) == "VARCHAR"
      assert Sqlite.visit({:type, "CHAR(10)"}, []) == "CHAR"
    end

    test "handles various SQLite type patterns" do
      # INTEGER family
      assert Sqlite.visit({:type, "BIGINT"}, []) == "BIGINT"
      assert Sqlite.visit({:type, "TINYINT"}, []) == "TINYINT"
      assert Sqlite.visit({:type, "SMALLINT"}, []) == "SMALLINT"
      assert Sqlite.visit({:type, "MEDIUMINT"}, []) == "MEDIUMINT"
      assert Sqlite.visit({:type, "INT2"}, []) == "INT2"
      assert Sqlite.visit({:type, "INT8"}, []) == "INT8"
      assert Sqlite.visit({:type, "UNSIGNED BIG INT"}, []) == "UNSIGNED BIG INT"

      # TEXT family
      assert Sqlite.visit({:type, "CHARACTER(20)"}, []) == "CHARACTER"
      assert Sqlite.visit({:type, "VARCHAR(255)"}, []) == "VARCHAR"
      assert Sqlite.visit({:type, "VARYING CHARACTER(255)"}, []) == "VARYING CHARACTER"
      assert Sqlite.visit({:type, "NCHAR(55)"}, []) == "NCHAR"
      assert Sqlite.visit({:type, "NATIVE CHARACTER(70)"}, []) == "NATIVE CHARACTER"
      assert Sqlite.visit({:type, "NVARCHAR(100)"}, []) == "NVARCHAR"
      assert Sqlite.visit({:type, "CLOB"}, []) == "CLOB"

      # REAL family
      assert Sqlite.visit({:type, "DOUBLE"}, []) == "DOUBLE"
      assert Sqlite.visit({:type, "DOUBLE PRECISION"}, []) == "DOUBLE PRECISION"
      assert Sqlite.visit({:type, "FLOAT"}, []) == "FLOAT"

      # Other types
      assert Sqlite.visit({:type, "NUMERIC"}, []) == "NUMERIC"
      assert Sqlite.visit({:type, "DECIMAL(10,5)"}, []) == "DECIMAL"
      assert Sqlite.visit({:type, "BOOLEAN"}, []) == "BOOLEAN"
      assert Sqlite.visit({:type, "DATE"}, []) == "DATE"
      assert Sqlite.visit({:type, "DATETIME"}, []) == "DATETIME"
    end
  end

  describe "default value edge cases" do
    test "handles empty and whitespace defaults" do
      assert Sqlite.visit({:default, ""}, []) == nil
      assert Sqlite.visit({:default, "   "}, []) == "   "
      assert Sqlite.visit({:default, "\t"}, []) == "\t"
    end

    test "handles complex quoted strings" do
      assert Sqlite.visit({:default, "'hello world'"}, []) == "hello world"
      assert Sqlite.visit({:default, "\"hello world\""}, []) == "hello world"
      assert Sqlite.visit({:default, "'don''t'"}, []) == "don't"
      assert Sqlite.visit({:default, "\"say \"\"hello\"\"\""}, []) == "say \"hello\""
    end

    test "handles multiline strings" do
      multiline = "'line1\nline2'"
      assert Sqlite.visit({:default, multiline}, []) == "line1\nline2"
    end

    test "handles negative numbers" do
      assert Sqlite.visit({:default, "-123"}, []) == -123
      assert Sqlite.visit({:default, "-45.67"}, []) == -45.67
    end

    test "handles scientific notation" do
      # Treated as string
      assert Sqlite.visit({:default, "1.23e4"}, []) == "1.23e4"
      # Treated as string
      assert Sqlite.visit({:default, "1.23E-4"}, []) == "1.23E-4"
    end

    test "handles special SQLite values" do
      assert Sqlite.visit({:default, "CURRENT_TIMESTAMP"}, []) == :current_timestamp
      assert Sqlite.visit({:default, "current_timestamp"}, []) == :current_timestamp
      assert Sqlite.visit({:default, "CURRENT_DATE"}, []) == :current_date
      assert Sqlite.visit({:default, "CURRENT_TIME"}, []) == :current_time
    end

    test "handles hex binary edge cases" do
      # Empty binary
      assert Sqlite.visit({:default, "x''"}, []) == ""
      # Uppercase X
      assert Sqlite.visit({:default, "X'48656C6C6F'"}, []) == "Hello"
      # Lowercase hex
      assert Sqlite.visit({:default, "x'48656c6c6f'"}, []) == "Hello"
    end

    test "handles malformed hex binary" do
      # Should return original value if hex decode fails
      assert Sqlite.visit({:default, "x'invalid'"}, []) == "x'invalid'"
      # Odd length
      assert Sqlite.visit({:default, "x'48656C6C6'"}, []) == "x'48656C6C6'"
    end
  end

  describe "boolean detection in Types.Sqlite" do
    alias Drops.SQL.Types.Sqlite, as: SqliteTypes

    test "detects boolean columns by default value" do
      assert SqliteTypes.is_boolean_column?("is_active", %{default: 1})
      assert SqliteTypes.is_boolean_column?("is_active", %{default: 0})
      assert SqliteTypes.is_boolean_column?("is_active", %{default: true})
      assert SqliteTypes.is_boolean_column?("is_active", %{default: false})
      refute SqliteTypes.is_boolean_column?("count", %{default: 42})
    end

    test "detects boolean columns by name patterns" do
      # Prefixes
      assert SqliteTypes.is_boolean_column?("is_active", %{})
      assert SqliteTypes.is_boolean_column?("has_permission", %{})
      assert SqliteTypes.is_boolean_column?("can_edit", %{})
      assert SqliteTypes.is_boolean_column?("should_notify", %{})
      assert SqliteTypes.is_boolean_column?("will_expire", %{})
      assert SqliteTypes.is_boolean_column?("allow_comments", %{})
      assert SqliteTypes.is_boolean_column?("enable_feature", %{})
      assert SqliteTypes.is_boolean_column?("disable_ads", %{})

      # Suffixes
      assert SqliteTypes.is_boolean_column?("feature_enabled", %{})
      assert SqliteTypes.is_boolean_column?("ads_disabled", %{})
      assert SqliteTypes.is_boolean_column?("user_active", %{})
      assert SqliteTypes.is_boolean_column?("post_inactive", %{})
      assert SqliteTypes.is_boolean_column?("debug_flag", %{})

      # Common boolean words
      assert SqliteTypes.is_boolean_column?("active", %{})
      assert SqliteTypes.is_boolean_column?("enabled", %{})
      assert SqliteTypes.is_boolean_column?("visible", %{})
      assert SqliteTypes.is_boolean_column?("published", %{})
      assert SqliteTypes.is_boolean_column?("deleted", %{})

      # Non-boolean names
      refute SqliteTypes.is_boolean_column?("name", %{})
      refute SqliteTypes.is_boolean_column?("count", %{})
      refute SqliteTypes.is_boolean_column?("user_id", %{})
    end

    test "detects ROWID aliases" do
      assert SqliteTypes.is_rowid_alias?(%{type: :integer, meta: %{primary_key: true}})
      refute SqliteTypes.is_rowid_alias?(%{type: :integer, meta: %{primary_key: false}})
      refute SqliteTypes.is_rowid_alias?(%{type: :string, meta: %{primary_key: true}})
    end

    test "detects autoincrement behavior" do
      assert SqliteTypes.has_autoincrement?(%{type: :integer, meta: %{primary_key: true}})
      assert SqliteTypes.has_autoincrement?(%{meta: %{autoincrement: true}})
      refute SqliteTypes.has_autoincrement?(%{type: :integer, meta: %{primary_key: false}})
      refute SqliteTypes.has_autoincrement?(%{type: :string, meta: %{primary_key: true}})
    end
  end
end
