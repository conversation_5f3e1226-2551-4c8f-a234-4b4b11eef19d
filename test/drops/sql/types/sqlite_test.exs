defmodule Drops.SQL.Types.SqliteTest do
  use ExUnit.Case, async: true

  alias Drops.SQL.Types.Sqlite

  describe "to_ecto_type/1" do
    test "maps basic SQLite types to Ecto types using type affinity" do
      assert Sqlite.to_ecto_type(%{type: "INTEGER"}) == :integer
      assert Sqlite.to_ecto_type(%{type: "TEXT"}) == :string
      assert Sqlite.to_ecto_type(%{type: "REAL"}) == :float
      assert Sqlite.to_ecto_type(%{type: "BLOB"}) == :binary
      assert Sqlite.to_ecto_type(%{type: "BOOLEAN"}) == :boolean
    end

    test "handles SQLite type affinity patterns" do
      # INTEGER affinity
      assert Sqlite.to_ecto_type(%{type: "BIGINT"}) == :integer
      assert Sqlite.to_ecto_type(%{type: "TINYINT"}) == :integer
      assert Sqlite.to_ecto_type(%{type: "SMALLINT"}) == :integer
      assert Sqlite.to_ecto_type(%{type: "MEDIUMINT"}) == :integer
      assert Sqlite.to_ecto_type(%{type: "INT2"}) == :integer
      assert Sqlite.to_ecto_type(%{type: "INT8"}) == :integer
      assert Sqlite.to_ecto_type(%{type: "UNSIGNED BIG INT"}) == :integer

      # TEXT affinity
      assert Sqlite.to_ecto_type(%{type: "CHARACTER"}) == :string
      assert Sqlite.to_ecto_type(%{type: "VARCHAR"}) == :string
      assert Sqlite.to_ecto_type(%{type: "VARYING CHARACTER"}) == :string
      assert Sqlite.to_ecto_type(%{type: "NCHAR"}) == :string
      assert Sqlite.to_ecto_type(%{type: "NATIVE CHARACTER"}) == :string
      assert Sqlite.to_ecto_type(%{type: "NVARCHAR"}) == :string
      assert Sqlite.to_ecto_type(%{type: "CLOB"}) == :string

      # REAL affinity
      assert Sqlite.to_ecto_type(%{type: "DOUBLE"}) == :float
      assert Sqlite.to_ecto_type(%{type: "DOUBLE PRECISION"}) == :float
      assert Sqlite.to_ecto_type(%{type: "FLOAT"}) == :float

      # NUMERIC affinity
      assert Sqlite.to_ecto_type(%{type: "NUMERIC"}) == :decimal
      assert Sqlite.to_ecto_type(%{type: "DECIMAL"}) == :decimal

      # Special types
      assert Sqlite.to_ecto_type(%{type: "DATE"}) == :date
      assert Sqlite.to_ecto_type(%{type: "TIME"}) == :time
      assert Sqlite.to_ecto_type(%{type: "DATETIME"}) == :naive_datetime
      assert Sqlite.to_ecto_type(%{type: "TIMESTAMP"}) == :naive_datetime
      assert Sqlite.to_ecto_type(%{type: "JSON"}) == :map
    end

    test "handles UUID type mapping" do
      # When only type is provided, returns :uuid
      assert Sqlite.to_ecto_type(%{type: "UUID"}) == :uuid

      # When full column info is provided, returns :binary_id
      column = %{type: "UUID", meta: %{}, name: "id"}
      assert Sqlite.to_ecto_type(column) == :binary_id
    end

    test "handles unknown types (defaults to binary)" do
      assert Sqlite.to_ecto_type(%{type: "UNKNOWN_TYPE"}) == :binary
      assert Sqlite.to_ecto_type(%{type: "CUSTOM"}) == :binary
    end

    test "detects boolean columns from INTEGER type" do
      # Boolean by default value
      column = %{type: "INTEGER", meta: %{default: 1}, name: "is_active"}
      assert Sqlite.to_ecto_type(column) == :boolean

      column = %{type: "INTEGER", meta: %{default: 0}, name: "count"}
      assert Sqlite.to_ecto_type(column) == :boolean

      # Boolean by name pattern
      column = %{type: "INTEGER", meta: %{}, name: "is_enabled"}
      assert Sqlite.to_ecto_type(column) == :boolean

      column = %{type: "INTEGER", meta: %{}, name: "has_permission"}
      assert Sqlite.to_ecto_type(column) == :boolean

      # Not boolean
      column = %{type: "INTEGER", meta: %{default: 42}, name: "count"}
      assert Sqlite.to_ecto_type(column) == :integer

      column = %{type: "INTEGER", meta: %{}, name: "user_id"}
      assert Sqlite.to_ecto_type(column) == :integer
    end

    test "handles case-insensitive type matching" do
      assert Sqlite.to_ecto_type(%{type: "integer"}) == :integer
      assert Sqlite.to_ecto_type(%{type: "Integer"}) == :integer
      assert Sqlite.to_ecto_type(%{type: "text"}) == :string
      assert Sqlite.to_ecto_type(%{type: "Text"}) == :string
    end
  end

  describe "boolean detection" do
    test "detects boolean columns by default value" do
      assert Sqlite.is_boolean_column?("is_active", %{default: 1})
      assert Sqlite.is_boolean_column?("is_active", %{default: 0})
      assert Sqlite.is_boolean_column?("is_active", %{default: true})
      assert Sqlite.is_boolean_column?("is_active", %{default: false})
      refute Sqlite.is_boolean_column?("count", %{default: 42})
    end

    test "detects boolean columns by name patterns" do
      # Prefixes
      assert Sqlite.is_boolean_column?("is_active", %{})
      assert Sqlite.is_boolean_column?("has_permission", %{})
      assert Sqlite.is_boolean_column?("can_edit", %{})
      assert Sqlite.is_boolean_column?("should_notify", %{})
      assert Sqlite.is_boolean_column?("will_expire", %{})
      assert Sqlite.is_boolean_column?("allow_comments", %{})
      assert Sqlite.is_boolean_column?("enable_feature", %{})
      assert Sqlite.is_boolean_column?("disable_ads", %{})

      # Suffixes
      assert Sqlite.is_boolean_column?("feature_enabled", %{})
      assert Sqlite.is_boolean_column?("ads_disabled", %{})
      assert Sqlite.is_boolean_column?("user_active", %{})
      assert Sqlite.is_boolean_column?("post_inactive", %{})
      assert Sqlite.is_boolean_column?("debug_flag", %{})

      # Common boolean words
      assert Sqlite.is_boolean_column?("active", %{})
      assert Sqlite.is_boolean_column?("enabled", %{})
      assert Sqlite.is_boolean_column?("visible", %{})
      assert Sqlite.is_boolean_column?("published", %{})
      assert Sqlite.is_boolean_column?("deleted", %{})

      # Non-boolean names
      refute Sqlite.is_boolean_column?("name", %{})
      refute Sqlite.is_boolean_column?("count", %{})
      refute Sqlite.is_boolean_column?("user_id", %{})
    end

    test "handles atom column names" do
      assert Sqlite.is_boolean_column?(:is_active, %{})
      assert Sqlite.is_boolean_column?(:has_permission, %{})
      refute Sqlite.is_boolean_column?(:user_id, %{})
    end
  end

  describe "ROWID detection" do
    test "detects ROWID aliases" do
      assert Sqlite.is_rowid_alias?(%{type: :integer, meta: %{primary_key: true}})
      refute Sqlite.is_rowid_alias?(%{type: :integer, meta: %{primary_key: false}})
      refute Sqlite.is_rowid_alias?(%{type: :string, meta: %{primary_key: true}})
    end
  end

  describe "autoincrement detection" do
    test "detects autoincrement behavior" do
      assert Sqlite.has_autoincrement?(%{type: :integer, meta: %{primary_key: true}})
      assert Sqlite.has_autoincrement?(%{meta: %{autoincrement: true}})
      refute Sqlite.has_autoincrement?(%{type: :integer, meta: %{primary_key: false}})
      refute Sqlite.has_autoincrement?(%{type: :string, meta: %{primary_key: true}})
    end
  end
end
