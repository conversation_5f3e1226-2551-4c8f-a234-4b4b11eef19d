defmodule Drops.SQL.Types.SqliteCoercion do
  @moduledoc """
  SQLite-specific type coercion following ActiveRecord's cast → serialize → quote chain.

  This module provides comprehensive type coercion for SQLite, handling edge cases
  and value validation according to SQLite's type affinity system.

  ## Coercion Chain

  1. **Cast**: Convert user input to appropriate Elixir type
  2. **Serialize**: Convert Elixir type to database-compatible value
  3. **Quote**: Escape value for SQL statement inclusion

  ## Usage

      # Cast user input
      {:ok, value} = SqliteCoercion.cast(:integer, "123")
      # value = 123

      # Serialize for database storage
      serialized = SqliteCoercion.serialize(:integer, 123)
      # serialized = 123

      # Quote for SQL inclusion
      quoted = SqliteCoercion.quote(:integer, 123)
      # quoted = "123"
  """

  @doc """
  Casts a value to the specified Ecto type following SQLite's type affinity rules.

  ## Parameters

  - `ecto_type` - The target Ecto type atom
  - `value` - The value to cast

  ## Returns

  - `{:ok, casted_value}` - Successfully casted value
  - `{:error, reason}` - Cast failed with reason

  ## Examples

      iex> SqliteCoercion.cast(:integer, "123")
      {:ok, 123}

      iex> SqliteCoercion.cast(:boolean, 1)
      {:ok, true}

      iex> SqliteCoercion.cast(:string, 123)
      {:ok, "123"}
  """
  @spec cast(atom(), term()) :: {:ok, term()} | {:error, term()}
  def cast(ecto_type, value)

  # Integer casting
  def cast(:integer, value) when is_integer(value), do: {:ok, value}
  def cast(:integer, value) when is_binary(value) do
    case Integer.parse(value) do
      {int, ""} -> validate_integer_range(int)
      {int, _remainder} -> validate_integer_range(int)
      :error -> {:error, :invalid_integer}
    end
  end
  def cast(:integer, value) when is_float(value), do: {:ok, trunc(value)}
  def cast(:integer, true), do: {:ok, 1}
  def cast(:integer, false), do: {:ok, 0}
  def cast(:integer, nil), do: {:ok, nil}
  def cast(:integer, _), do: {:error, :invalid_integer}

  # Boolean casting (SQLite stores as INTEGER 0/1)
  def cast(:boolean, value) when is_boolean(value), do: {:ok, value}
  def cast(:boolean, 1), do: {:ok, true}
  def cast(:boolean, 0), do: {:ok, false}
  def cast(:boolean, value) when is_binary(value) do
    case String.downcase(String.trim(value)) do
      v when v in ["true", "t", "1", "yes", "y", "on"] -> {:ok, true}
      v when v in ["false", "f", "0", "no", "n", "off"] -> {:ok, false}
      "" -> {:ok, nil}
      _ -> {:error, :invalid_boolean}
    end
  end
  def cast(:boolean, nil), do: {:ok, nil}
  def cast(:boolean, _), do: {:error, :invalid_boolean}

  # String casting
  def cast(:string, value) when is_binary(value), do: {:ok, value}
  def cast(:string, value) when is_atom(value), do: {:ok, Atom.to_string(value)}
  def cast(:string, value) when is_number(value), do: {:ok, to_string(value)}
  def cast(:string, true), do: {:ok, "true"}
  def cast(:string, false), do: {:ok, "false"}
  def cast(:string, nil), do: {:ok, nil}
  def cast(:string, value), do: {:ok, inspect(value)}

  # Float casting
  def cast(:float, value) when is_float(value), do: {:ok, value}
  def cast(:float, value) when is_integer(value), do: {:ok, value * 1.0}
  def cast(:float, value) when is_binary(value) do
    case Float.parse(value) do
      {float, ""} -> validate_float_value(float)
      {float, _remainder} -> validate_float_value(float)
      :error -> {:error, :invalid_float}
    end
  end
  def cast(:float, nil), do: {:ok, nil}
  def cast(:float, _), do: {:error, :invalid_float}

  # Decimal casting
  def cast(:decimal, %Decimal{} = value), do: {:ok, value}
  def cast(:decimal, value) when is_number(value) do
    {:ok, Decimal.new(value)}
  end
  def cast(:decimal, value) when is_binary(value) do
    case Decimal.parse(value) do
      {decimal, ""} -> {:ok, decimal}
      {decimal, _remainder} -> {:ok, decimal}
      :error -> {:error, :invalid_decimal}
    end
  end
  def cast(:decimal, nil), do: {:ok, nil}
  def cast(:decimal, _), do: {:error, :invalid_decimal}

  # Binary casting
  def cast(:binary, value) when is_binary(value), do: {:ok, value}
  def cast(:binary, nil), do: {:ok, nil}
  def cast(:binary, _), do: {:error, :invalid_binary}

  # Date/Time casting
  def cast(:date, %Date{} = value), do: {:ok, value}
  def cast(:date, value) when is_binary(value) do
    case Date.from_iso8601(value) do
      {:ok, date} -> {:ok, date}
      {:error, _} -> {:error, :invalid_date}
    end
  end
  def cast(:date, nil), do: {:ok, nil}
  def cast(:date, _), do: {:error, :invalid_date}

  def cast(:time, %Time{} = value), do: {:ok, value}
  def cast(:time, value) when is_binary(value) do
    case Time.from_iso8601(value) do
      {:ok, time} -> {:ok, time}
      {:error, _} -> {:error, :invalid_time}
    end
  end
  def cast(:time, nil), do: {:ok, nil}
  def cast(:time, _), do: {:error, :invalid_time}

  def cast(:naive_datetime, %NaiveDateTime{} = value), do: {:ok, value}
  def cast(:naive_datetime, value) when is_binary(value) do
    case NaiveDateTime.from_iso8601(value) do
      {:ok, datetime} -> {:ok, datetime}
      {:error, _} -> {:error, :invalid_datetime}
    end
  end
  def cast(:naive_datetime, nil), do: {:ok, nil}
  def cast(:naive_datetime, _), do: {:error, :invalid_datetime}

  # Map/JSON casting
  def cast(:map, value) when is_map(value), do: {:ok, value}
  def cast(:map, value) when is_binary(value) do
    case Jason.decode(value) do
      {:ok, map} -> {:ok, map}
      {:error, _} -> {:error, :invalid_json}
    end
  end
  def cast(:map, nil), do: {:ok, nil}
  def cast(:map, _), do: {:error, :invalid_map}

  # UUID casting
  def cast(:uuid, value) when is_binary(value) do
    case Ecto.UUID.cast(value) do
      {:ok, uuid} -> {:ok, uuid}
      :error -> {:error, :invalid_uuid}
    end
  end
  def cast(:uuid, nil), do: {:ok, nil}
  def cast(:uuid, _), do: {:error, :invalid_uuid}

  # Fallback for unknown types
  def cast(_type, value), do: {:ok, value}

  @doc """
  Serializes a value for database storage following SQLite's storage classes.

  ## Parameters

  - `ecto_type` - The Ecto type atom
  - `value` - The value to serialize

  ## Returns

  The serialized value ready for database storage.
  """
  @spec serialize(atom(), term()) :: term()
  def serialize(_type, nil), do: nil

  # Integer serialization
  def serialize(:integer, value) when is_integer(value), do: value
  def serialize(:integer, value), do: value

  # Boolean serialization (SQLite stores as INTEGER 0/1)
  def serialize(:boolean, true), do: 1
  def serialize(:boolean, false), do: 0
  def serialize(:boolean, value), do: value

  # String serialization
  def serialize(:string, value) when is_binary(value), do: value
  def serialize(:string, value), do: to_string(value)

  # Float serialization
  def serialize(:float, value) when is_float(value), do: value
  def serialize(:float, value), do: value

  # Decimal serialization (SQLite stores as TEXT)
  def serialize(:decimal, %Decimal{} = value), do: Decimal.to_string(value)
  def serialize(:decimal, value), do: value

  # Binary serialization
  def serialize(:binary, value) when is_binary(value), do: value
  def serialize(:binary, value), do: value

  # Date/Time serialization (SQLite stores as TEXT in ISO 8601)
  def serialize(:date, %Date{} = value), do: Date.to_iso8601(value)
  def serialize(:date, value), do: value

  def serialize(:time, %Time{} = value), do: Time.to_iso8601(value)
  def serialize(:time, value), do: value

  def serialize(:naive_datetime, %NaiveDateTime{} = value), do: NaiveDateTime.to_iso8601(value)
  def serialize(:naive_datetime, value), do: value

  # Map/JSON serialization (SQLite stores as TEXT)
  def serialize(:map, value) when is_map(value), do: Jason.encode!(value)
  def serialize(:map, value), do: value

  # UUID serialization
  def serialize(:uuid, value) when is_binary(value), do: value
  def serialize(:uuid, value), do: value

  # Fallback
  def serialize(_type, value), do: value

  @doc """
  Quotes a value for SQL statement inclusion.

  ## Parameters

  - `ecto_type` - The Ecto type atom
  - `value` - The value to quote

  ## Returns

  The quoted value as a string ready for SQL inclusion.
  """
  @spec quote(atom(), term()) :: String.t()
  def quote(_type, nil), do: "NULL"

  # Numeric types don't need quoting
  def quote(type, value) when type in [:integer, :float] and is_number(value) do
    if is_finite_number?(value) do
      to_string(value)
    else
      "'#{value}'"  # Quote infinite/NaN values
    end
  end

  # Boolean quoting (as INTEGER)
  def quote(:boolean, true), do: "1"
  def quote(:boolean, false), do: "0"

  # String quoting with proper escaping
  def quote(type, value) when type in [:string, :decimal, :date, :time, :naive_datetime, :map, :uuid] and is_binary(value) do
    escaped = String.replace(value, "'", "''")
    "'#{escaped}'"
  end

  # Binary quoting as hex
  def quote(:binary, value) when is_binary(value) do
    hex = Base.encode16(value, case: :upper)
    "x'#{hex}'"
  end

  # Fallback
  def quote(_type, value) when is_binary(value) do
    escaped = String.replace(value, "'", "''")
    "'#{escaped}'"
  end
  def quote(_type, value), do: to_string(value)

  # Private helper functions

  @spec validate_integer_range(integer()) :: {:ok, integer()} | {:error, :out_of_range}
  defp validate_integer_range(value) do
    # SQLite INTEGER can store 8 bytes (-9223372036854775808 to 9223372036854775807)
    if value >= -9_223_372_036_854_775_808 and value <= 9_223_372_036_854_775_807 do
      {:ok, value}
    else
      {:error, :out_of_range}
    end
  end

  @spec validate_float_value(float()) :: {:ok, float()} | {:error, :invalid_float}
  defp validate_float_value(value) do
    if is_finite_number?(value) or value in [:infinity, :negative_infinity, :nan] do
      {:ok, value}
    else
      {:error, :invalid_float}
    end
  end

  @spec is_finite_number?(term()) :: boolean()
  defp is_finite_number?(value) when is_number(value) do
    not (value == :infinity or value == :negative_infinity or value != value)
  end
  defp is_finite_number?(_), do: false
end
