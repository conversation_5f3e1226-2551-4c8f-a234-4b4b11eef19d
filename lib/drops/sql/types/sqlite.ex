defmodule Drops.SQL.Types.Sqlite do
  @moduledoc """
  SQLite-specific type conversion and detection logic.

  This module provides enhanced type detection for SQLite, particularly
  for boolean columns which are stored as INTEGER in SQLite but should
  be treated as :boolean in Ecto schemas.
  """

  alias Drops.SQL.Database

  @doc """
  Converts a SQLite column to the appropriate Ecto type.

  This function analyzes column metadata to make intelligent type decisions,
  particularly for boolean detection in SQLite's INTEGER columns.

  ## Boolean Detection

  SQLite doesn't have a native boolean type, so booleans are stored as INTEGER.
  This function detects boolean columns by analyzing:

  1. Default values (0, 1, true, false)
  2. Check constraints that limit values to boolean-like ranges
  3. Column names that suggest boolean usage

  ## Examples

      iex> column = %{type: :integer, meta: %{default: 1}}
      iex> Drops.SQL.Types.Sqlite.to_ecto_type(column)
      :boolean

      iex> column = %{type: :integer, meta: %{default: 42}}
      iex> Drops.SQL.Types.Sqlite.to_ecto_type(column)
      :integer
  """
  @spec to_ecto_type(map()) :: atom()
  def to_ecto_type(%{type: type, meta: meta, name: name}) do
    # First, map SQLite type to Ecto type using type affinity rules
    base_ecto_type = map_sqlite_type_to_ecto(type)

    # Then apply SQLite-specific logic (e.g., boolean detection for INTEGER)
    case base_ecto_type do
      :integer ->
        if is_boolean_column?(name, meta) do
          :boolean
        else
          :integer
        end

      :uuid ->
        :binary_id

      other ->
        other
    end
  end

  def to_ecto_type(%{type: type}) do
    map_sqlite_type_to_ecto(type)
  end

  # Maps SQLite types to Ecto types following SQLite's type affinity rules
  @spec map_sqlite_type_to_ecto(String.t()) :: atom()
  defp map_sqlite_type_to_ecto(type) when is_binary(type) do
    normalized_type = String.upcase(type)

    cond do
      # INTEGER affinity - exact matches first
      normalized_type == "INTEGER" -> :integer
      normalized_type == "INT" -> :integer
      normalized_type == "TINYINT" -> :integer
      normalized_type == "SMALLINT" -> :integer
      normalized_type == "MEDIUMINT" -> :integer
      normalized_type == "BIGINT" -> :integer
      normalized_type == "UNSIGNED BIG INT" -> :integer
      normalized_type == "INT2" -> :integer
      normalized_type == "INT8" -> :integer
      # INTEGER affinity - pattern matching
      String.contains?(normalized_type, "INT") -> :integer
      # TEXT affinity - exact matches first
      normalized_type == "TEXT" -> :string
      normalized_type == "CLOB" -> :string
      # TEXT affinity - pattern matching
      String.contains?(normalized_type, "CHAR") -> :string
      String.contains?(normalized_type, "CLOB") -> :string
      String.contains?(normalized_type, "TEXT") -> :string
      # BLOB affinity
      normalized_type == "BLOB" -> :binary
      String.contains?(normalized_type, "BLOB") -> :binary
      # REAL affinity - exact matches first
      normalized_type == "REAL" -> :float
      normalized_type == "DOUBLE" -> :float
      normalized_type == "DOUBLE PRECISION" -> :float
      normalized_type == "FLOAT" -> :float
      # REAL affinity - pattern matching
      String.contains?(normalized_type, "REAL") -> :float
      String.contains?(normalized_type, "FLOA") -> :float
      String.contains?(normalized_type, "DOUB") -> :float
      # NUMERIC affinity
      normalized_type in ["NUMERIC", "DECIMAL"] -> :decimal
      String.contains?(normalized_type, "NUMERIC") -> :decimal
      String.contains?(normalized_type, "DECIMAL") -> :decimal
      # Special types (exact matches)
      normalized_type == "BOOLEAN" -> :boolean
      normalized_type == "BOOL" -> :boolean
      normalized_type == "DATE" -> :date
      normalized_type == "TIME" -> :time
      normalized_type in ["DATETIME", "TIMESTAMP"] -> :naive_datetime
      normalized_type == "UUID" -> :uuid
      normalized_type == "JSON" -> :map
      # Default: if no affinity matches, SQLite treats it as BLOB affinity
      true -> :binary
    end
  end

  defp map_sqlite_type_to_ecto(type), do: type

  @doc """
  Detects if a column is a SQLite ROWID alias.

  In SQLite, an INTEGER PRIMARY KEY column becomes an alias for the built-in ROWID.
  This function detects such columns and marks them appropriately.

  ## Examples

      iex> column = %{type: :integer, meta: %{primary_key: true}, name: "id"}
      iex> Drops.SQL.Types.Sqlite.is_rowid_alias?(column)
      true

      iex> column = %{type: :integer, meta: %{primary_key: false}, name: "count"}
      iex> Drops.SQL.Types.Sqlite.is_rowid_alias?(column)
      false
  """
  @spec is_rowid_alias?(map()) :: boolean()
  def is_rowid_alias?(%{type: :integer, meta: %{primary_key: true}}) do
    # In SQLite, INTEGER PRIMARY KEY becomes a ROWID alias
    true
  end

  def is_rowid_alias?(_), do: false

  @doc """
  Detects if a column has autoincrement behavior.

  SQLite's INTEGER PRIMARY KEY columns have autoincrement behavior by default,
  even without the AUTOINCREMENT keyword.

  ## Examples

      iex> column = %{type: :integer, meta: %{primary_key: true, autoincrement: true}}
      iex> Drops.SQL.Types.Sqlite.has_autoincrement?(column)
      true
  """
  @spec has_autoincrement?(map()) :: boolean()
  def has_autoincrement?(%{type: :integer, meta: %{primary_key: true}}) do
    # INTEGER PRIMARY KEY in SQLite has autoincrement behavior
    true
  end

  def has_autoincrement?(%{meta: %{autoincrement: true}}), do: true
  def has_autoincrement?(_), do: false

  @doc """
  Converts a column's metadata when it's detected as boolean.

  This function converts integer default values (0, 1) to boolean values
  (false, true) when a column is detected as boolean.
  """
  @spec convert_boolean_metadata(Database.Column.t()) :: Database.Column.t()
  def convert_boolean_metadata(%Database.Column{type: "integer", meta: meta, name: name} = column) do
    if is_boolean_column?(name, meta) do
      updated_meta = convert_boolean_default(meta)
      %{column | meta: updated_meta}
    else
      column
    end
  end

  def convert_boolean_metadata(column), do: column

  # Converts integer default values to boolean
  @spec convert_boolean_default(map()) :: map()
  defp convert_boolean_default(%{default: 1} = meta), do: %{meta | default: true}
  defp convert_boolean_default(%{default: 0} = meta), do: %{meta | default: false}
  defp convert_boolean_default(meta), do: meta

  # Determines if an INTEGER column should be treated as boolean
  @spec is_boolean_column?(atom() | String.t(), map()) :: boolean()
  def is_boolean_column?(name, meta) do
    cond do
      # Check default values for boolean indicators
      has_boolean_default?(meta) -> true
      # Check constraints for boolean patterns
      has_boolean_constraint?(meta) -> true
      # Check column name for boolean patterns
      has_boolean_name?(name) -> true
      # Default to integer
      true -> false
    end
  end

  # Checks if the default value suggests a boolean column
  @spec has_boolean_default?(map()) :: boolean()
  defp has_boolean_default?(%{default: default}) when default in [0, 1, true, false], do: true
  defp has_boolean_default?(_), do: false

  # Checks if check constraints suggest a boolean column
  @spec has_boolean_constraint?(map()) :: boolean()
  defp has_boolean_constraint?(%{check_constraints: constraints}) when is_list(constraints) do
    Enum.any?(constraints, &is_boolean_constraint?/1)
  end

  defp has_boolean_constraint?(_), do: false

  # Checks if a constraint expression suggests boolean values
  @spec is_boolean_constraint?(String.t()) :: boolean()
  defp is_boolean_constraint?(constraint) when is_binary(constraint) do
    normalized = String.downcase(constraint)

    cond do
      # Check for explicit 0/1 constraints
      String.contains?(normalized, "in (0, 1)") -> true
      String.contains?(normalized, "in (1, 0)") -> true
      # Check for range constraints that suggest boolean
      String.match?(normalized, ~r/>=\s*0\s+and\s+<=\s*1/) -> true
      String.match?(normalized, ~r/between\s+0\s+and\s+1/) -> true
      # Check for explicit boolean value constraints
      String.contains?(normalized, "true") or String.contains?(normalized, "false") -> true
      true -> false
    end
  end

  defp is_boolean_constraint?(_), do: false

  # Checks if the column name suggests boolean usage
  @spec has_boolean_name?(atom() | String.t()) :: boolean()
  defp has_boolean_name?(name) when is_atom(name), do: has_boolean_name?(Atom.to_string(name))

  defp has_boolean_name?(name) when is_binary(name) do
    normalized = String.downcase(name)

    cond do
      # Common boolean prefixes
      String.starts_with?(normalized, "is_") ->
        true

      String.starts_with?(normalized, "has_") ->
        true

      String.starts_with?(normalized, "can_") ->
        true

      String.starts_with?(normalized, "should_") ->
        true

      String.starts_with?(normalized, "will_") ->
        true

      String.starts_with?(normalized, "allow_") ->
        true

      String.starts_with?(normalized, "enable_") ->
        true

      String.starts_with?(normalized, "disable_") ->
        true

      # Common boolean suffixes
      String.ends_with?(normalized, "_enabled") ->
        true

      String.ends_with?(normalized, "_disabled") ->
        true

      String.ends_with?(normalized, "_active") ->
        true

      String.ends_with?(normalized, "_inactive") ->
        true

      String.ends_with?(normalized, "_flag") ->
        true

      String.ends_with?(normalized, "_status") and String.length(normalized) < 20 ->
        true

      # Common boolean words
      normalized in [
        "active",
        "enabled",
        "disabled",
        "visible",
        "hidden",
        "public",
        "private",
        "published",
        "draft",
        "archived",
        "deleted",
        "confirmed",
        "verified",
        "approved",
        "rejected"
      ] ->
        true

      true ->
        false
    end
  end

  defp has_boolean_name?(_), do: false
end
