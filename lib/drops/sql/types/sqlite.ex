defmodule Drops.SQL.Types.Sqlite do
  @moduledoc """
  SQLite-specific type conversion and detection logic.

  This module provides enhanced type detection for SQLite, particularly
  for boolean columns which are stored as INTEGER in SQLite but should
  be treated as :boolean in Ecto schemas.
  """

  alias Drops.SQL.Database

  @doc """
  Converts a SQLite column to the appropriate Ecto type.

  This function analyzes column metadata to make intelligent type decisions,
  particularly for boolean detection in SQLite's INTEGER columns.

  ## Boolean Detection

  SQLite doesn't have a native boolean type, so booleans are stored as INTEGER.
  This function detects boolean columns by analyzing:

  1. Default values (0, 1, true, false)
  2. Check constraints that limit values to boolean-like ranges
  3. Column names that suggest boolean usage

  ## Examples

      iex> column = %{type: :integer, meta: %{default: 1}}
      iex> Drops.SQL.Types.Sqlite.to_ecto_type(column)
      :boolean

      iex> column = %{type: :integer, meta: %{default: 42}}
      iex> Drops.SQL.Types.Sqlite.to_ecto_type(column)
      :integer
  """
  @spec to_ecto_type(map()) :: atom()
  def to_ecto_type(%{type: :integer} = column) do
    if is_boolean_column?(column) do
      :boolean
    else
      :integer
    end
  end

  def to_ecto_type(%{type: :uuid}), do: :binary_id

  def to_ecto_type(%{type: type}), do: type

  @doc """
  Converts a column's metadata when it's detected as boolean.

  This function converts integer default values (0, 1) to boolean values
  (false, true) when a column is detected as boolean.
  """
  @spec convert_boolean_metadata(Database.Column.t()) :: Database.Column.t()
  def convert_boolean_metadata(%Database.Column{type: "integer", meta: meta} = column) do
    if is_boolean_column?(column) do
      updated_meta = convert_boolean_default(meta)
      %{column | meta: updated_meta}
    else
      column
    end
  end

  def convert_boolean_metadata(column), do: column

  # Converts integer default values to boolean
  @spec convert_boolean_default(map()) :: map()
  defp convert_boolean_default(%{default: 1} = meta), do: %{meta | default: true}
  defp convert_boolean_default(%{default: 0} = meta), do: %{meta | default: false}
  defp convert_boolean_default(meta), do: meta

  # Determines if an INTEGER column should be treated as boolean
  @spec is_boolean_column?(Database.Column.t()) :: boolean()
  defp is_boolean_column?(%Database.Column{type: "integer", meta: meta, name: name}) do
    cond do
      # Check default values for boolean indicators
      has_boolean_default?(meta) -> true
      # Check constraints for boolean patterns
      has_boolean_constraint?(meta) -> true
      # Check column name for boolean patterns
      has_boolean_name?(name) -> true
      # Default to integer
      true -> false
    end
  end

  defp is_boolean_column?(_), do: false

  # Checks if the default value suggests a boolean column
  @spec has_boolean_default?(map()) :: boolean()
  defp has_boolean_default?(%{default: default}) when default in [0, 1, true, false], do: true
  defp has_boolean_default?(_), do: false

  # Checks if check constraints suggest a boolean column
  @spec has_boolean_constraint?(map()) :: boolean()
  defp has_boolean_constraint?(%{check_constraints: constraints}) when is_list(constraints) do
    Enum.any?(constraints, &is_boolean_constraint?/1)
  end

  defp has_boolean_constraint?(_), do: false

  # Checks if a constraint expression suggests boolean values
  @spec is_boolean_constraint?(String.t()) :: boolean()
  defp is_boolean_constraint?(constraint) when is_binary(constraint) do
    normalized = String.downcase(constraint)

    cond do
      # Check for explicit 0/1 constraints
      String.contains?(normalized, "in (0, 1)") -> true
      String.contains?(normalized, "in (1, 0)") -> true
      # Check for range constraints that suggest boolean
      String.match?(normalized, ~r/>=\s*0\s+and\s+<=\s*1/) -> true
      String.match?(normalized, ~r/between\s+0\s+and\s+1/) -> true
      # Check for explicit boolean value constraints
      String.contains?(normalized, "true") or String.contains?(normalized, "false") -> true
      true -> false
    end
  end

  defp is_boolean_constraint?(_), do: false

  # Checks if the column name suggests boolean usage
  @spec has_boolean_name?(atom() | String.t()) :: boolean()
  defp has_boolean_name?(name) when is_atom(name), do: has_boolean_name?(Atom.to_string(name))

  defp has_boolean_name?(name) when is_binary(name) do
    normalized = String.downcase(name)

    cond do
      # Common boolean prefixes
      String.starts_with?(normalized, "is_") ->
        true

      String.starts_with?(normalized, "has_") ->
        true

      String.starts_with?(normalized, "can_") ->
        true

      String.starts_with?(normalized, "should_") ->
        true

      String.starts_with?(normalized, "will_") ->
        true

      String.starts_with?(normalized, "allow_") ->
        true

      String.starts_with?(normalized, "enable_") ->
        true

      String.starts_with?(normalized, "disable_") ->
        true

      # Common boolean suffixes
      String.ends_with?(normalized, "_enabled") ->
        true

      String.ends_with?(normalized, "_disabled") ->
        true

      String.ends_with?(normalized, "_active") ->
        true

      String.ends_with?(normalized, "_inactive") ->
        true

      String.ends_with?(normalized, "_flag") ->
        true

      String.ends_with?(normalized, "_status") and String.length(normalized) < 20 ->
        true

      # Common boolean words
      normalized in [
        "active",
        "enabled",
        "disabled",
        "visible",
        "hidden",
        "public",
        "private",
        "published",
        "draft",
        "archived",
        "deleted",
        "confirmed",
        "verified",
        "approved",
        "rejected"
      ] ->
        true

      true ->
        false
    end
  end

  defp has_boolean_name?(_), do: false
end
