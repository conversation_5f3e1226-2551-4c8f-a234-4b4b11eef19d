defmodule Drops.SQL.Compilers.Sqlite do
  @moduledoc """
  SQLite-specific compiler for processing database introspection ASTs.

  This module implements the `Drops.SQL.Compiler` behavior to provide SQLite-specific
  type mapping and AST processing. It converts SQLite database types to Ecto types
  and handles SQLite-specific type characteristics.

  ## SQLite Type System

  SQLite uses a dynamic type system with type affinity rather than strict types.
  This compiler maps SQLite's type affinities to appropriate Ecto types:

  ### Numeric Types
  - `INTEGER` → `:integer`
  - `REAL`, `FLOAT` → `:float`
  - `NUMERIC`, `DECIMAL` → `:decimal`

  ### Text Types
  - `TEXT` → `:string`
  - Character types (VARCHAR, CHAR, etc.) → `:string`

  ### Binary Types
  - `BLOB` → `:binary`

  ### Boolean Types
  - `BOOLEAN`, `BOOL` → `:boolean`

  ### Date/Time Types
  - `DATE` → `:date`
  - `TIME` → `:time`
  - `DATETIME`, `TIM<PERSON><PERSON><PERSON>` → `:naive_datetime`

  ### Special Types
  - `UUID` → `:uuid`
  - `JSON` → `:map`

  ## Type Affinity Rules

  SQLite's type affinity rules are respected:
  - Types containing "INT" are mapped to `:integer`
  - Types containing "CHAR", "CLOB", or "TEXT" are mapped to `:string`
  - Types containing "BLOB" or no affinity are mapped to `:binary`
  - Types containing "REAL", "FLOA", or "DOUB" are mapped to `:float`

  ## Usage

  This compiler is typically used automatically by the `Drops.SQL.Sqlite` adapter:

      # Automatic usage through adapter
      {:ok, table} = Drops.SQL.Sqlite.table("users", MyRepo)

      # Direct usage (advanced)
      ast = {:table, {{:identifier, "users"}, columns, [], []}}
      table = Drops.SQL.Compilers.Sqlite.process(ast, adapter: :sqlite)

  ## Implementation Notes

  - Case-insensitive type matching (SQLite is case-insensitive)
  - Handles both exact type names and type affinity patterns
  - Preserves unknown types as-is for custom handling
  - Supports SQLite's flexible typing system
  """

  use Drops.SQL.Compiler

  @doc """
  Visits a type AST node and maps SQLite types to Ecto types.

  This function implements SQLite-specific type mapping, handling SQLite's
  dynamic type system and type affinity rules. It follows SQLite's type affinity
  algorithm to determine the appropriate Ecto type.

  ## Parameters

  - `{:type, type}` - Type AST node with SQLite type name
  - `opts` - Processing options (may contain metadata for enhanced type detection)

  ## Returns

  Ecto type atom (`:integer`, `:string`, etc.) or the original type if unmapped.

  ## Examples

      iex> Drops.SQL.Compilers.Sqlite.visit({:type, "INTEGER"}, [])
      :integer

      iex> Drops.SQL.Compilers.Sqlite.visit({:type, "VARCHAR(255)"}, [])
      :string

      iex> Drops.SQL.Compilers.Sqlite.visit({:type, "DECIMAL(10,2)"}, [])
      :decimal

      iex> Drops.SQL.Compilers.Sqlite.visit({:type, "BLOB"}, [])
      :binary
  """
  @spec visit({:type, String.t()}, keyword()) :: atom() | String.t()
  def visit({:type, type}, opts) do
    normalized_type = String.upcase(type)

    # Extract precision and scale information if present
    {base_type, precision, scale} = extract_type_parameters(normalized_type)

    # Store precision/scale in opts for later use
    updated_opts =
      if precision || scale do
        opts
        |> Keyword.put(:precision, precision)
        |> Keyword.put(:scale, scale)
      else
        opts
      end

    map_sqlite_type_to_ecto(base_type, updated_opts)
  end

  def visit({:default, nil}, _opts), do: nil
  def visit({:default, ""}, _opts), do: nil

  def visit({:default, value}, _opts) when is_binary(value) do
    extract_default_value(value)
  end

  def visit({:meta, meta}, opts) do
    # First, process each value in the meta map (including default values)
    processed_meta =
      Enum.reduce(meta, %{}, fn {key, value}, acc ->
        Map.put(acc, key, visit(value, opts))
      end)

    # Then add precision and scale from opts if available
    case {opts[:precision], opts[:scale]} do
      {nil, nil} ->
        processed_meta

      {precision, nil} ->
        Map.put(processed_meta, :precision, precision)

      {precision, scale} ->
        processed_meta
        |> Map.put(:precision, precision)
        |> Map.put(:scale, scale)
    end
  end

  # Extracts and parses SQLite default values following ActiveRecord's approach
  @spec extract_default_value(String.t()) :: term()
  defp extract_default_value(value) do
    trimmed = String.trim(value)

    cond do
      # Handle NULL values (case-insensitive)
      String.match?(trimmed, ~r/^null$/i) ->
        nil

      # Handle hex binary values: x'hexstring'
      String.match?(trimmed, ~r/^x'([0-9a-fA-F]*)'$/i) ->
        extract_hex_binary(trimmed)

      # Handle single-quoted strings with proper escaping
      String.match?(trimmed, ~r/^'.*'$/m) ->
        extract_quoted_string(trimmed, "'")

      # Handle double-quoted strings with proper escaping
      String.match?(trimmed, ~r/^".*"$/m) ->
        extract_quoted_string(trimmed, "\"")

      # Handle boolean literals (case-insensitive)
      String.match?(trimmed, ~r/^(true|false)$/i) ->
        String.downcase(trimmed) |> String.to_existing_atom()

      # Handle numeric values (integers)
      String.match?(trimmed, ~r/^-?\d+$/) ->
        String.to_integer(trimmed)

      # Handle numeric values (floats)
      String.match?(trimmed, ~r/^-?\d+\.\d+$/) ->
        String.to_float(trimmed)

      # Handle SQLite function expressions
      String.match?(trimmed, ~r/^CURRENT_TIMESTAMP$/i) ->
        :current_timestamp

      String.match?(trimmed, ~r/^CURRENT_DATE$/i) ->
        :current_date

      String.match?(trimmed, ~r/^CURRENT_TIME$/i) ->
        :current_time

      # Handle other function expressions (return as-is for now)
      String.match?(trimmed, ~r/^[A-Z_]+\(.*\)$/i) ->
        trimmed

      # Default: return the trimmed value as-is
      true ->
        trimmed
    end
  end

  # Extracts hex binary value from x'hexstring' format
  @spec extract_hex_binary(String.t()) :: binary()
  defp extract_hex_binary(value) do
    case Regex.run(~r/^x'([0-9a-fA-F]*)'$/i, value) do
      [_full, hex_string] ->
        case Base.decode16(hex_string, case: :mixed) do
          {:ok, binary} -> binary
          # Return original if decode fails
          :error -> value
        end

      nil ->
        # Return original if regex doesn't match
        value
    end
  end

  # Extracts quoted string with proper escape handling
  @spec extract_quoted_string(String.t(), String.t()) :: String.t()
  defp extract_quoted_string(value, quote_char) do
    # Remove outer quotes
    inner = String.slice(value, 1..-2//1)

    # Handle escaped quotes ('' becomes ' and "" becomes ")
    case quote_char do
      "'" -> String.replace(inner, "''", "'")
      "\"" -> String.replace(inner, "\"\"", "\"")
      _ -> inner
    end
  end

  # Maps SQLite types to Ecto types following SQLite's type affinity rules
  @spec map_sqlite_type_to_ecto(String.t(), keyword()) :: atom()
  defp map_sqlite_type_to_ecto(type, _opts) do
    cond do
      # INTEGER affinity - exact matches first
      type == "INTEGER" -> :integer
      type == "INT" -> :integer
      type == "TINYINT" -> :integer
      type == "SMALLINT" -> :integer
      type == "MEDIUMINT" -> :integer
      type == "BIGINT" -> :integer
      type == "UNSIGNED BIG INT" -> :integer
      type == "INT2" -> :integer
      type == "INT8" -> :integer
      # INTEGER affinity - pattern matching
      String.contains?(type, "INT") -> :integer
      # TEXT affinity - exact matches first
      type == "TEXT" -> :string
      type == "CLOB" -> :string
      # TEXT affinity - pattern matching
      String.contains?(type, "CHAR") -> :string
      String.contains?(type, "CLOB") -> :string
      String.contains?(type, "TEXT") -> :string
      # BLOB affinity
      type == "BLOB" -> :binary
      String.contains?(type, "BLOB") -> :binary
      # REAL affinity - exact matches first
      type == "REAL" -> :float
      type == "DOUBLE" -> :float
      type == "DOUBLE PRECISION" -> :float
      type == "FLOAT" -> :float
      # REAL affinity - pattern matching
      String.contains?(type, "REAL") -> :float
      String.contains?(type, "FLOA") -> :float
      String.contains?(type, "DOUB") -> :float
      # NUMERIC affinity
      type in ["NUMERIC", "DECIMAL"] -> :decimal
      String.contains?(type, "NUMERIC") -> :decimal
      String.contains?(type, "DECIMAL") -> :decimal
      # Special types (exact matches)
      type == "BOOLEAN" -> :boolean
      type == "BOOL" -> :boolean
      type == "DATE" -> :date
      type == "TIME" -> :time
      type in ["DATETIME", "TIMESTAMP"] -> :naive_datetime
      type == "UUID" -> :uuid
      type == "JSON" -> :map
      # Default: if no affinity matches, SQLite treats it as BLOB affinity
      true -> :binary
    end
  end

  # Extracts precision and scale from parameterized types like DECIMAL(10,2), VARCHAR(255)
  @spec extract_type_parameters(String.t()) :: {String.t(), integer() | nil, integer() | nil}
  def extract_type_parameters(type) do
    case Regex.run(~r/^([A-Z\s]+)\((\d+)(?:,\s*(\d+))?\)$/, type) do
      [_full, base_type, precision_str] ->
        precision = String.to_integer(precision_str)
        {String.trim(base_type), precision, nil}

      [_full, base_type, precision_str, scale_str] ->
        precision = String.to_integer(precision_str)
        scale = String.to_integer(scale_str)
        {String.trim(base_type), precision, scale}

      nil ->
        {type, nil, nil}
    end
  end
end
